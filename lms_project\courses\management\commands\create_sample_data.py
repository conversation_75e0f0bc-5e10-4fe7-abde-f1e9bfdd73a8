from django.core.management.base import BaseCommand
from courses.models import Category, Course
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Creates initial data for the LMS'

    def handle(self, *args, **kwargs):
        # Create categories
        categories = [
            {
                'name': 'Programming',
                'description': 'Learn programming languages and software development'
            },
            {
                'name': 'Web Development',
                'description': 'Master web development technologies and frameworks'
            },
            {
                'name': 'Artificial Intelligence',
                'description': 'Explore AI, machine learning, and data science'
            },
            {
                'name': 'Database Systems',
                'description': 'Learn database design, management, and optimization'
            },
            {
                'name': 'Mobile Development',
                'description': 'Build mobile applications for iOS and Android'
            },
            {
                'name': 'Cloud Computing',
                'description': 'Master cloud platforms and services'
            }
        ]
        
        for category in categories:
            Category.objects.get_or_create(
                name=category['name'],
                description=category['description']
            )
            self.stdout.write(self.style.SUCCESS(f'Created category: {category["name"]}'))

        # Get admin user
        admin = User.objects.get(username='admin')

        # Create sample courses
        sample_courses = [
            {
                'title': 'Python Programming for Beginners',
                'description': 'Learn Python programming from scratch. This comprehensive course covers fundamental concepts, data structures, and practical applications.',
                'category': 'Programming'
            },
            {
                'title': 'Full Stack Web Development with Django',
                'description': 'Master web development using Django framework. Build powerful web applications from frontend to backend.',
                'category': 'Web Development'
            },
            {
                'title': 'Introduction to Artificial Intelligence',
                'description': 'Explore the fundamentals of AI, including machine learning, neural networks, and practical applications in the real world.',
                'category': 'Artificial Intelligence'
            },
            {
                'title': 'SQL and Database Design',
                'description': 'Learn database design principles and master SQL for efficient data management and querying.',
                'category': 'Database Systems'
            },
            {
                'title': 'React Native Mobile Development',
                'description': 'Build cross-platform mobile applications using React Native framework. Create beautiful and responsive mobile UIs.',
                'category': 'Mobile Development'
            },
            {
                'title': 'AWS Cloud Services Essential',
                'description': 'Get started with Amazon Web Services. Learn cloud computing concepts and essential AWS services.',
                'category': 'Cloud Computing'
            }
        ]

        for course_data in sample_courses:
            try:
                category = Category.objects.get(name=course_data['category'])
                course, created = Course.objects.get_or_create(
                    title=course_data['title'],
                    defaults={
                        'description': course_data['description'],
                        'category': category,
                        'instructor': admin,
                        'is_published': True
                    }
                )
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created course: {course_data["title"]}'))
                else:
                    self.stdout.write(self.style.SUCCESS(f'Course already exists: {course_data["title"]}'))
            except Category.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Category not found: {course_data["category"]}'))
