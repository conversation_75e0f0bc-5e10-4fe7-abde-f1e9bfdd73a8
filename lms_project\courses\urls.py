from django.urls import path
from . import views

app_name = 'courses'

urlpatterns = [
    path('', views.CourseListView.as_view(), name='course_list'),
    path('create/', views.CourseCreateView.as_view(), name='course_create'),
    path('<int:pk>/', views.CourseDetailView.as_view(), name='course_detail'),
    path('<int:pk>/edit/', views.CourseUpdateView.as_view(), name='course_update'),
    path('<int:pk>/delete/', views.CourseDeleteView.as_view(), name='course_delete'),
    path('<int:pk>/publish/', views.CoursePublishView.as_view(), name='course_publish'),
    path('<int:pk>/enroll/', views.CourseEnrollView.as_view(), name='course_enroll'),
    path('<int:pk>/change-instructor/', views.CourseInstructorUpdateView.as_view(), name='course_change_instructor'),
    path('instructor/', views.InstructorDashboardView.as_view(), name='instructor_dashboard'),
    path('my-courses/', views.StudentDashboardView.as_view(), name='my_courses'),
]
