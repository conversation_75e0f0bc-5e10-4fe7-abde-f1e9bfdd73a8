from rest_framework import serializers
from .models import Course, Category, Lesson, Enrollment, Progress

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'

class LessonSerializer(serializers.ModelSerializer):
    class Meta:
        model = Lesson
        fields = ('id', 'title', 'description', 'order', 'created_at', 'updated_at')

class CourseSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    instructor_name = serializers.CharField(source='instructor.get_full_name', read_only=True)
    lessons = LessonSerializer(many=True, read_only=True)
    enrolled_students_count = serializers.SerializerMethodField()

    class Meta:
        model = Course
        fields = ('id', 'title', 'description', 'category', 'category_name',
                 'instructor', 'instructor_name', 'cover_image', 'created_at',
                 'updated_at', 'is_published', 'lessons', 'enrolled_students_count')
        read_only_fields = ('instructor', 'created_at', 'updated_at')

    def get_enrolled_students_count(self, obj):
        return obj.students.count()

class EnrollmentSerializer(serializers.ModelSerializer):
    course_title = serializers.CharField(source='course.title', read_only=True)
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)

    class Meta:
        model = Enrollment
        fields = ('id', 'student', 'student_name', 'course', 'course_title',
                 'enrolled_at', 'completed', 'last_accessed')
        read_only_fields = ('enrolled_at', 'last_accessed')

class ProgressSerializer(serializers.ModelSerializer):
    lesson_title = serializers.CharField(source='lesson.title', read_only=True)

    class Meta:
        model = Progress
        fields = ('id', 'enrollment', 'lesson', 'lesson_title', 'completed', 'completed_at')
