from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from .models import Quiz, Question, Choice, QuizAttempt, StudentAnswer
from .serializers import (QuizSerializer, QuestionSerializer, ChoiceSerializer,
                         QuizAttemptSerializer, StudentAnswerSerializer)
from courses.models import Course
from accounts.permissions import IsInstructorOrReadOnly, IsEnrolledOrInstructor

class QuizViewSet(viewsets.ModelViewSet):
    queryset = Quiz.objects.all()
    serializer_class = QuizSerializer
    permission_classes = [permissions.IsAuthenticated, IsInstructorOrReadOnly]
    filterset_fields = ['course', 'lesson']

    def get_queryset(self):
        if self.request.user.role == 'instructor':
            return Quiz.objects.filter(course__instructor=self.request.user)
        return Quiz.objects.filter(course__students=self.request.user)

    @action(detail=True, methods=['post'])
    def start_attempt(self, request, pk=None):
        quiz = self.get_object()
        if not quiz.course.students.filter(id=request.user.id).exists():
            return Response(
                {'detail': 'Not enrolled in this course.'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        attempt = QuizAttempt.objects.create(
            quiz=quiz,
            student=request.user
        )
        serializer = QuizAttemptSerializer(attempt)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def submit_attempt(self, request, pk=None):
        quiz = self.get_object()
        attempt = QuizAttempt.objects.get(
            quiz=quiz,
            student=request.user,
            completed_at__isnull=True
        )
        
        answers_data = request.data.get('answers', [])
        total_points = 0
        max_points = 0
        
        for answer_data in answers_data:
            question = Question.objects.get(id=answer_data['question_id'])
            max_points += question.points
            
            if question.question_type == 'essay':
                student_answer = StudentAnswer.objects.create(
                    attempt=attempt,
                    question=question,
                    essay_answer=answer_data['answer'],
                    is_correct=None
                )
            else:
                selected_choices = Choice.objects.filter(
                    id__in=answer_data.get('selected_choices', [])
                )
                is_correct = all(
                    choice.is_correct for choice in selected_choices
                ) and len(selected_choices) == question.choices.filter(
                    is_correct=True
                ).count()
                
                points_earned = question.points if is_correct else 0
                total_points += points_earned
                
                student_answer = StudentAnswer.objects.create(
                    attempt=attempt,
                    question=question,
                    is_correct=is_correct,
                    points_earned=points_earned
                )
                student_answer.selected_choices.set(selected_choices)
        
        attempt.completed_at = timezone.now()
        attempt.score = (total_points / max_points) * 100 if max_points > 0 else 0
        attempt.passed = attempt.score >= quiz.passing_score
        attempt.save()
        
        serializer = QuizAttemptSerializer(attempt)
        return Response(serializer.data)

class QuestionViewSet(viewsets.ModelViewSet):
    queryset = Question.objects.all()
    serializer_class = QuestionSerializer
    permission_classes = [permissions.IsAuthenticated, IsInstructorOrReadOnly]
    filterset_fields = ['quiz', 'question_type']

    def get_queryset(self):
        if self.request.user.role == 'instructor':
            return Question.objects.filter(quiz__course__instructor=self.request.user)
        return Question.objects.filter(quiz__course__students=self.request.user)
