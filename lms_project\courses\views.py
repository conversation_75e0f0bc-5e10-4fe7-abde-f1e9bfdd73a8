from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.views.generic.base import View
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils import timezone
from .models import Course, Category, Lesson, Enrollment, Progress
from .serializers import (CourseSerializer, CategorySerializer, LessonSerializer,
                        EnrollmentSerializer, ProgressSerializer)
from accounts.permissions import IsInstructorOrReadOnly, IsEnrolledOrInstructor

class CategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAdminUser]

class CourseViewSet(viewsets.ModelViewSet):
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [permissions.IsAuthenticated, IsInstructorOrReadOnly]
    filterset_fields = ['category', 'instructor', 'is_published']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at', 'title']

    def perform_create(self, serializer):
        serializer.save(instructor=self.request.user)

    @action(detail=True, methods=['post'])
    def enroll(self, request, pk=None):
        course = self.get_object()
        student = request.user
        
        if course.students.filter(id=student.id).exists():
            return Response(
                {'detail': 'Already enrolled in this course.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        enrollment = Enrollment.objects.create(student=student, course=course)
        serializer = EnrollmentSerializer(enrollment)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        course = self.get_object()
        if not course.students.filter(id=request.user.id).exists():
            return Response(
                {'detail': 'Not enrolled in this course.'},
                status=status.HTTP_403_FORBIDDEN
            )
            
        enrollment = Enrollment.objects.get(student=request.user, course=course)
        progress = Progress.objects.filter(enrollment=enrollment)
        serializer = ProgressSerializer(progress, many=True)
        return Response(serializer.data)

class CourseListView(ListView):
    model = Course
    template_name = 'courses/course_list.html'
    context_object_name = 'courses'
    paginate_by = 9
    
    def get_queryset(self):
        queryset = Course.objects.filter(is_published=True)
        category_id = self.request.GET.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        context['selected_category'] = self.request.GET.get('category')
        return context


class InstructorDashboardView(LoginRequiredMixin, ListView):
    """Dashboard for instructors showing courses they teach."""
    model = Course
    template_name = 'courses/instructor_dashboard.html'
    context_object_name = 'courses'
    paginate_by = 12

    def get_queryset(self):
        return Course.objects.filter(instructor=self.request.user).order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['total_courses'] = self.get_queryset().count()
        context['total_students'] = Enrollment.objects.filter(course__in=self.get_queryset()).count()
        return context


class StudentDashboardView(LoginRequiredMixin, ListView):
    """Dashboard for students showing enrolled courses and progress."""
    model = Enrollment
    template_name = 'courses/student_dashboard.html'
    context_object_name = 'enrollments'
    paginate_by = 12

    def get_queryset(self):
        return Enrollment.objects.filter(student=self.request.user).select_related('course').order_by('-enrolled_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # compute progress percentage per enrollment so template can render it easily
        enrollments = list(self.get_queryset())
        for enrollment in enrollments:
            total_lessons = enrollment.course.lessons.count()
            completed = Progress.objects.filter(enrollment=enrollment, completed=True).count()
            percentage = int((completed / total_lessons * 100) if total_lessons > 0 else 0)
            # attach a read-only attribute used by the template
            setattr(enrollment, 'progress_percentage', percentage)

        context['enrollments'] = enrollments
        context['total_enrolled'] = len(enrollments)
        return context

class CourseDetailView(DetailView):
    model = Course
    template_name = 'courses/course_detail.html'
    context_object_name = 'course'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.user.is_authenticated:
            context['is_enrolled'] = self.object.students.filter(id=self.request.user.id).exists()
            if context['is_enrolled']:
                enrollment = Enrollment.objects.get(student=self.request.user, course=self.object)
                completed_lessons = Progress.objects.filter(enrollment=enrollment, completed=True).count()
                total_lessons = self.object.lessons.count()
                context['progress'] = {
                    'completed_lessons': completed_lessons,
                    'total_lessons': total_lessons,
                    'percentage': int((completed_lessons / total_lessons * 100) if total_lessons > 0 else 0)
                }
        context['lessons'] = self.object.lessons.all().order_by('order')
        return context

class CourseCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = Course
    template_name = 'courses/course_form.html'
    fields = ['title', 'description', 'category', 'cover_image']
    success_url = reverse_lazy('courses:course_list')

    def test_func(self):
        return self.request.user.role == 'instructor'

    def form_valid(self, form):
        form.instance.instructor = self.request.user
        messages.success(self.request, 'Course created successfully!')
        return super().form_valid(form)

class CourseUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Course
    template_name = 'courses/course_form.html'
    fields = ['title', 'description', 'category', 'cover_image', 'is_published']

    def test_func(self):
        course = self.get_object()
        return self.request.user == course.instructor

    def form_valid(self, form):
        messages.success(self.request, 'Course updated successfully!')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('courses:course_detail', kwargs={'pk': self.object.pk})

class CourseDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    model = Course
    template_name = 'courses/course_confirm_delete.html'
    success_url = reverse_lazy('courses:course_list')

    def test_func(self):
        course = self.get_object()
        return self.request.user == course.instructor

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, 'Course deleted successfully!')
        return super().delete(request, *args, **kwargs)

class CoursePublishView(LoginRequiredMixin, UserPassesTestMixin, View):
    def test_func(self):
        course = get_object_or_404(Course, pk=self.kwargs['pk'])
        return self.request.user == course.instructor

    def post(self, request, *args, **kwargs):
        course = get_object_or_404(Course, pk=kwargs['pk'])
        course.is_published = True
        course.save()
        messages.success(request, 'Course published successfully!')
        return redirect('courses:course_detail', pk=course.pk)

class CourseEnrollView(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        course = get_object_or_404(Course, pk=kwargs['pk'])
        if course.students.filter(id=request.user.id).exists():
            messages.warning(request, 'You are already enrolled in this course.')
        else:
            enrollment = Enrollment.objects.create(
                student=request.user,
                course=course,
                enrolled_at=timezone.now()
            )
            messages.success(request, 'Successfully enrolled in the course!')
        return redirect('courses:course_detail', pk=course.pk)

class CourseInstructorUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Course
    template_name = 'courses/course_instructor_form.html'
    fields = ['instructor']
    
    def test_func(self):
        # Only admin can change instructors
        return self.request.user.is_staff
    
    def form_valid(self, form):
        messages.success(self.request, 'Course instructor updated successfully!')
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse_lazy('courses:course_detail', kwargs={'pk': self.object.pk})

class LessonViewSet(viewsets.ModelViewSet):
    queryset = Lesson.objects.all()
    serializer_class = LessonSerializer
    permission_classes = [permissions.IsAuthenticated, IsInstructorOrReadOnly]
    filterset_fields = ['course']

    def get_queryset(self):
        if self.request.user.role == 'instructor':
            return Lesson.objects.filter(course__instructor=self.request.user)
        return Lesson.objects.filter(course__students=self.request.user)

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        lesson = self.get_object()
        enrollment = Enrollment.objects.get(
            student=request.user,
            course=lesson.course
        )
        
        progress, created = Progress.objects.get_or_create(
            enrollment=enrollment,
            lesson=lesson,
            defaults={'completed': True, 'completed_at': timezone.now()}
        )
        
        if not created:
            progress.completed = True
            progress.completed_at = timezone.now()
            progress.save()
            
        serializer = ProgressSerializer(progress)
        return Response(serializer.data)
