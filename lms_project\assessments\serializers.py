from rest_framework import serializers
from .models import Quiz, Question, Choice, QuizAttempt, StudentAnswer

class ChoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Choice
        fields = ('id', 'text', 'is_correct')

class QuestionSerializer(serializers.ModelSerializer):
    choices = ChoiceSerializer(many=True, read_only=True)

    class Meta:
        model = Question
        fields = ('id', 'quiz', 'question_type', 'text', 'points', 'order', 'choices')

class QuizSerializer(serializers.ModelSerializer):
    questions_count = serializers.SerializerMethodField()
    total_points = serializers.SerializerMethodField()

    class Meta:
        model = Quiz
        fields = ('id', 'course', 'lesson', 'title', 'description', 'time_limit',
                 'passing_score', 'created_at', 'updated_at', 'questions_count',
                 'total_points')

    def get_questions_count(self, obj):
        return obj.questions.count()

    def get_total_points(self, obj):
        return sum(question.points for question in obj.questions.all())

class StudentAnswerSerializer(serializers.ModelSerializer):
    class Meta:
        model = StudentAnswer
        fields = ('id', 'attempt', 'question', 'selected_choices', 'essay_answer',
                 'is_correct', 'points_earned')

class QuizAttemptSerializer(serializers.ModelSerializer):
    answers = StudentAnswerSerializer(many=True, read_only=True)

    class Meta:
        model = QuizAttempt
        fields = ('id', 'quiz', 'student', 'started_at', 'completed_at',
                 'score', 'passed', 'answers')
