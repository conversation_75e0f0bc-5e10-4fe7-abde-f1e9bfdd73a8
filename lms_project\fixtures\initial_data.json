[{"model": "accounts.user", "pk": 1, "fields": {"password": "pbkdf2_sha256$600000$dLcHJ9L4QsY5XqJ6q2KCZU$sN0DEhTd5IzMpA5YWlSg/jCZJaXhMrMFWGEFG9QVDZ4=", "last_login": null, "is_superuser": true, "username": "admin", "first_name": "Admin", "last_name": "User", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-09-02T00:00:00Z", "role": "admin", "bio": "System Administrator", "phone_number": ""}}, {"model": "accounts.user", "pk": 2, "fields": {"password": "pbkdf2_sha256$600000$dLcHJ9L4QsY5XqJ6q2KCZU$sN0DEhTd5IzMpA5YWlSg/jCZJaXhMrMFWGEFG9QVDZ4=", "last_login": null, "is_superuser": false, "username": "instructor1", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-09-02T00:00:00Z", "role": "instructor", "bio": "Programming Specialist In<PERSON><PERSON><PERSON>", "phone_number": ""}}, {"model": "courses.category", "pk": 1, "fields": {"name": "Programming", "description": "Courses in programming and software development"}}, {"model": "courses.category", "pk": 2, "fields": {"name": "Web Design", "description": "Courses in web design and development"}}, {"model": "courses.category", "pk": 3, "fields": {"name": "Artificial Intelligence", "description": "Courses in artificial intelligence and machine learning"}}, {"model": "courses.course", "pk": 1, "fields": {"title": "Python for Beginners", "description": "Learn Python programming basics from scratch. The course includes fundamental concepts and practical applications.", "category": 1, "instructor": 2, "created_at": "2025-09-02T00:00:00Z", "updated_at": "2025-09-02T00:00:00Z", "is_published": true}}, {"model": "courses.course", "pk": 2, "fields": {"title": "Web Development with Django", "description": "Learn web application development using Django framework. From basic concepts to advanced applications.", "category": 2, "instructor": 2, "created_at": "2025-09-02T00:00:00Z", "updated_at": "2025-09-02T00:00:00Z", "is_published": true}}, {"model": "courses.lesson", "pk": 1, "fields": {"course": 1, "title": "Introduction to Python", "description": "Learn Python basics and development environment", "order": 1}}, {"model": "courses.lesson", "pk": 2, "fields": {"course": 1, "title": "Variables and Data Types", "description": "Learn how to use variables and different data types in Python", "order": 2}}, {"model": "assessments.quiz", "pk": 1, "fields": {"course": 1, "lesson": 1, "title": "Python Introduction Quiz", "description": "A short quiz about Python basics", "time_limit": 30, "passing_score": 70, "created_at": "2025-09-02T00:00:00Z", "updated_at": "2025-09-02T00:00:00Z"}}, {"model": "assessments.question", "pk": 1, "fields": {"quiz": 1, "question_type": "multiple_choice", "text": "What is the file extension for Python files?", "points": 1, "order": 1}}, {"model": "assessments.choice", "pk": 1, "fields": {"question": 1, "text": ".py", "is_correct": true}}, {"model": "assessments.choice", "pk": 2, "fields": {"question": 1, "text": ".js", "is_correct": false}}, {"model": "assessments.choice", "pk": 3, "fields": {"question": 1, "text": ".php", "is_correct": false}}]