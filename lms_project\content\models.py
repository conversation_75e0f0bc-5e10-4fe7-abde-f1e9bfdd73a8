from django.db import models
from django.utils.translation import gettext_lazy as _
from courses.models import Lesson

class Content(models.Model):
    VIDEO = 'video'
    DOCUMENT = 'document'
    PRESENTATION = 'presentation'
    
    CONTENT_TYPES = [
        (VIDEO, _('Video')),
        (DOCUMENT, _('Document')),
        (PRESENTATION, _('Presentation')),
    ]
    
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name='contents')
    title = models.CharField(max_length=200)
    content_type = models.CharField(max_length=20, choices=CONTENT_TYPES)
    order = models.PositiveIntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Content')
        verbose_name_plural = _('Contents')
        ordering = ['order']
        
    def __str__(self):
        return f"{self.lesson.title} - {self.title}"

class Video(models.Model):
    content = models.OneToOneField(Content, on_delete=models.CASCADE, related_name='video')
    video_file = models.FileField(upload_to='course_videos/')
    duration = models.PositiveIntegerField(help_text=_('Duration in seconds'))
    thumbnail = models.ImageField(upload_to='video_thumbnails/', null=True, blank=True)
    
    class Meta:
        verbose_name = _('Video')
        verbose_name_plural = _('Videos')
        
    def __str__(self):
        return self.content.title

class Document(models.Model):
    content = models.OneToOneField(Content, on_delete=models.CASCADE, related_name='document')
    file = models.FileField(upload_to='course_documents/')
    file_type = models.CharField(max_length=50)
    
    class Meta:
        verbose_name = _('Document')
        verbose_name_plural = _('Documents')
        
    def __str__(self):
        return self.content.title
